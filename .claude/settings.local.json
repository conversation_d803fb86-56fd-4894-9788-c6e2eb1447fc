{"permissions": {"allow": ["Bash(pnpm format:*)", "Bash(pnpm lint:frontend:*)", "Bash(pnpm lint:*)", "Bash(pnpm --filter frontend lint)", "Bash(pnpm --filter frontend check)", "Bash(grep:*)", "Bash(rg:*)", "Bash(pnpm --filter frontend build)", "Bash(npm run check:*)", "Bash(uv run pytest:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(pnpm test:*)", "Bash(pnpm check:*)", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:keepachangelog.com)"], "deny": []}}