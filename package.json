{"name": "brainless-stats", "private": true, "version": "0.1.0", "description": "CS2 Demo Analysis Platform - Full Stack Monorepo", "type": "module", "scripts": {"setup": "pnpm install && cd packages/backend && uv run brainless-cli setup", "dev": "concurrently \"pnpm dev:frontend\" \"pnpm dev:backend\"", "dev:full": "cd packages/backend && uv run brainless-cli dev", "dev:frontend": "pnpm --filter frontend dev", "dev:backend": "cd packages/backend && uv run brainless-cli dev backend", "dev:demo-processing": "cd packages/backend && uv run brainless-cli dev demo-processing --detach", "build": "pnpm build:frontend && pnpm build:backend", "build:frontend": "pnpm --filter frontend build", "build:backend": "cd packages/backend && uv run brainless-cli build", "build:docker": "cd packages/backend && uv run brainless-cli build docker", "start:prod": "cd packages/backend && uv run brainless-cli dev --detach && docker-compose -f docker-compose.prod.yml up -d", "stop": "cd packages/backend && uv run brainless-cli stop", "restart": "pnpm stop && pnpm dev", "logs": "cd packages/backend && uv run brainless-cli logs", "logs:frontend": "cd packages/backend && uv run brainless-cli logs frontend", "logs:backend": "cd packages/backend && uv run brainless-cli logs backend", "logs:demo-processing": "cd packages/backend && uv run brainless-cli logs demo-processing", "shell:frontend": "cd packages/backend && uv run brainless-cli shell frontend", "shell:backend": "cd packages/backend && uv run brainless-cli shell backend", "shell:demo-processing": "cd packages/backend && uv run brainless-cli shell demo-processing", "shell:redis": "cd packages/backend && uv run brainless-cli shell redis", "test": "concurrently \"pnpm test:frontend\" \"pnpm test:backend\"", "test:frontend": "pnpm --filter frontend test:run", "test:backend": "cd packages/backend && uv run brainless-cli test", "test:e2e": "pnpm --filter frontend test:e2e", "test:coverage": "concurrently \"pnpm --filter frontend test:coverage\" \"cd packages/backend && uv run brainless-cli test --coverage\"", "lint": "concurrently \"pnpm lint:frontend\" \"pnpm lint:backend\"", "lint:frontend": "pnpm --filter frontend lint", "lint:backend": "cd packages/backend && uv run brainless-cli lint", "lint:fix": "concurrently \"pnpm --filter frontend lint --fix\" \"cd packages/backend && uv run brainless-cli lint --fix\"", "format": "concurrently \"pnpm format:frontend\" \"pnpm format:backend\"", "format:frontend": "pnpm --filter frontend format", "format:backend": "cd packages/backend && uv run brainless-cli format", "clean": "cd packages/backend && uv run brainless-cli clean", "clean:all": "cd packages/backend && uv run brainless-cli clean --all", "status": "cd packages/backend && uv run brainless-cli status", "health": "cd packages/backend && uv run brainless-cli health", "tasks": "node scripts/tasks.mjs", "schema:generate": "node scripts/tasks.mjs schema", "docker:up": "pnpm dev:full", "docker:down": "pnpm stop", "docker:logs": "pnpm logs", "docker:clean": "pnpm clean:all", "ci": "pnpm lint && pnpm test", "precommit": "pnpm lint:fix && pnpm format", "version:status": "node scripts/version-manager.mjs status", "version:patch": "node scripts/version-manager.mjs release patch", "version:minor": "node scripts/version-manager.mjs release minor", "version:major": "node scripts/version-manager.mjs release major"}, "workspaces": ["frontend"], "devDependencies": {"@types/node": "^22.0.0", "concurrently": "^9.0.0", "wait-on": "^8.0.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@9.0.0"}