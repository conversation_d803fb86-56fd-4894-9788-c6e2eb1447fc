# Brainless Stats

A comprehensive CS2 demo analysis platform built with modern technologies and best practices.

## 🏗️ Architecture

This is a monorepo containing:

- **Frontend**: SvelteKit application with TypeScript
- **Backend**: FastAPI application with Python
- **Demo Processing**: FastStream microservice for CS2 demo analysis
- **Infrastructure**: <PERSON><PERSON>, <PERSON><PERSON>, Nginx for production deployment

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+ and **pnpm** 8+
- **Python** 3.13+ and **uv**
- **Docker** and **Docker Compose**

### Development Setup

1. **Clone and setup**:

   ```bash
   git clone <repository-url>
   cd brainless-stats
   ./scripts/dev.sh setup
   ```

1. **Start development environment**:

   ```bash
   pnpm dev
   # or
   ./scripts/dev.sh dev
   ```

1. **Access the application**:

   - Frontend: <http://localhost:3000>
   - Backend API: <http://localhost:8000>
   - API Documentation: <http://localhost:8000/docs>

## 📁 Project Structure

```
brainless-stats/
├── frontend/                 # SvelteKit frontend application
│   ├── src/
│   ├── Dockerfile.dev
│   ├── Dockerfile.prod
│   └── package.json
├── backend/                  # FastAPI backend application
│   ├── src/backend/
│   ├── demo_processing/      # Demo processing microservice
│   ├── Dockerfile.dev
│   ├── Dockerfile.prod
│   └── pyproject.toml
├── nginx/                    # Nginx configuration
├── scripts/                  # Development scripts
├── .github/workflows/        # CI/CD pipelines
├── docker-compose.dev.yml    # Development environment
├── docker-compose.prod.yml   # Production environment
└── package.json             # Root workspace configuration
```

## 🛠️ Development

### Available Commands

```bash
# Setup and Development
pnpm setup                  # Initial project setup
pnpm dev                    # Start frontend + backend (recommended)
pnpm dev:full               # Start full Docker environment
pnpm dev:frontend           # Start only frontend
pnpm dev:backend            # Start only backend
pnpm dev:demo-processing    # Start only demo processing

# Building
pnpm build                  # Build all services
pnpm build:frontend         # Build frontend only
pnpm build:backend          # Build backend only
pnpm build:docker           # Build Docker images

# Testing
pnpm test                   # Run all tests
pnpm test:frontend          # Run frontend tests
pnpm test:backend           # Run backend tests
pnpm test:e2e               # Run end-to-end tests
pnpm test:coverage          # Run tests with coverage

# Code Quality
pnpm lint                   # Run linting for all services
pnpm lint:fix               # Auto-fix linting issues
pnpm format                 # Format code for all services
pnpm pre-commit             # Run pre-commit hooks on all files
pnpm pre-commit:install     # Install pre-commit hooks (one-time setup)

# Versioning & Releases
pnpm version:status         # Check version status across all packages
pnpm version:patch          # Create patch release (0.1.0 → 0.1.1)
pnpm version:minor          # Create minor release (0.1.0 → 0.2.0)
pnpm version:major          # Create major release (0.1.0 → 1.0.0)

# Production
pnpm start:prod             # Start production environment
pnpm stop                   # Stop all services
pnpm clean                  # Clean up containers
pnpm clean:all              # Clean up everything including volumes

# Utilities
pnpm logs                   # View all service logs
pnpm status                 # Show service status
pnpm health                 # Check service health
pnpm schema:generate        # Generate API schema
```

### Using the CLI Tools

We provide multiple ways to run tasks, choose what works best for you:

#### Universal Task Runner (Recommended)

```bash
python tasks.py --help

# Examples:
python tasks.py setup               # Complete project setup
python tasks.py dev                 # Start development environment
python tasks.py test --coverage     # Run tests with coverage
python tasks.py lint --fix          # Auto-fix linting issues
python tasks.py clean --all         # Clean everything
```

#### pnpm Scripts (Native to Node.js ecosystem)

```bash
pnpm --help

# Examples:
pnpm setup                          # Complete project setup
pnpm dev                            # Start development environment
pnpm test:coverage                  # Run tests with coverage
pnpm lint:fix                       # Auto-fix linting issues
pnpm clean:all                      # Clean everything
```

#### Python CLI (Backend-specific operations)

```bash
cd backend && uv run brainless-cli --help

# Examples:
uv run brainless-cli setup          # Setup backend environment
uv run brainless-cli dev             # Start full Docker environment
uv run brainless-cli dev backend     # Start only backend
uv run brainless-cli test --coverage # Run tests with coverage
uv run brainless-cli shell backend   # Open backend shell
uv run brainless-cli status          # Show service status
```

#### Node.js Task Runner (Frontend-specific operations)

```bash
node scripts/tasks.mjs --help

# Examples:
node scripts/tasks.mjs setup        # Setup frontend environment
node scripts/tasks.mjs dev          # Start frontend dev server
node scripts/tasks.mjs test:e2e     # Run E2E tests
node scripts/tasks.mjs schema       # Generate API schema
```

## 🐳 Docker Configuration

### Development Environment

- **Hot reloading** enabled for all services
- **Volume mounts** for source code
- **Debug logging** enabled
- **Development databases** (SQLite)

### Production Environment

- **Multi-stage builds** for optimized images
- **Non-root users** for security
- **Health checks** for all services
- **Resource limits** configured
- **Nginx reverse proxy** with SSL support

## 🛠️ Task Runner Philosophy

This project uses a **multi-layered task runner approach** that integrates well with modern development workflows:

1. **Universal Task Runner** (`python tasks.py`) - Simple, cross-platform interface
1. **pnpm Scripts** - Native Node.js ecosystem integration with workspace support
1. **Specialized CLIs** - Language-specific tools for advanced operations
1. **Docker Compose** - Container orchestration for full-stack development

This approach provides:

- ✅ **Flexibility**: Choose the tool that fits your workflow
- ✅ **Consistency**: Same commands work across different environments
- ✅ **Integration**: Seamless integration with IDEs and CI/CD
- ✅ **Discoverability**: Easy to find and understand available commands

## 🔧 Configuration

### Environment Variables

Copy and customize environment files:

```bash
cp .env.example .env
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

Key configuration options:

- `ENV`: Environment (development/staging/production)
- `DATABASE_URI`: Database connection string
- `REDIS_URL`: Redis connection URL
- `STEAM_API_KEY`: Steam API key for game data
- `DEMO_STORAGE_PATH`: Path for demo file storage

### Service Configuration

Each service can be configured independently:

- **Frontend**: `frontend/package.json` and environment variables
- **Backend**: `backend/pyproject.toml` and environment variables
- **Demo Processing**: `backend/demo_processing/pyproject.toml`

## 🧪 Testing

### Frontend Testing

```bash
cd frontend
pnpm test              # Unit tests with Vitest
pnpm test:e2e          # E2E tests with Playwright
pnpm test:coverage     # Coverage report
```

### Backend Testing

```bash
cd backend
uv run pytest         # Unit tests
uv run pytest --cov   # Coverage report
```

### Integration Testing

```bash
# Start services and run integration tests
docker-compose -f docker-compose.dev.yml up -d
# Run your integration test suite
docker-compose -f docker-compose.dev.yml down
```

## 🚀 Deployment

### Production Deployment

1. **Build production images**:

   ```bash
   pnpm build:docker
   ```

1. **Start production environment**:

   ```bash
   pnpm start:prod
   ```

1. **Configure SSL** (optional):

   - Place SSL certificates in `nginx/ssl/`
   - Uncomment HTTPS server block in `nginx/nginx.conf`

### CI/CD Pipeline

The project includes GitHub Actions workflows for:

- **Continuous Integration**: Testing, linting, type checking
- **Docker Image Building**: Multi-stage builds with caching
- **Deployment**: Automated deployment to staging/production
- **Release Management**: Automated releases with versioning and changelog updates

## 📊 Monitoring and Logging

### Development

- **Centralized logging** via Docker Compose
- **Health checks** for all services
- **Hot reloading** for rapid development

### Production

- **Structured logging** with appropriate log levels
- **Health check endpoints** for monitoring
- **Resource monitoring** via Docker stats
- **Nginx access logs** for request tracking

## 🔒 Security

### Development

- **Non-root users** in all containers
- **Environment variable isolation**
- **Network isolation** via Docker networks

### Production

- **SSL/TLS encryption** (when configured)
- **Security headers** via Nginx
- **Rate limiting** for API endpoints
- **Input validation** and sanitization

## 🤝 Contributing

1. **Fork the repository**
1. **Create a feature branch**: `git checkout -b feature/amazing-feature`
1. **Make your changes** and add tests
1. **Run the test suite**: `pnpm test && pnpm lint`
1. **Commit your changes**: `git commit -m 'Add amazing feature'`
1. **Push to the branch**: `git push origin feature/amazing-feature`
1. **Open a Pull Request**

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Troubleshooting

### Common Issues

1. **Docker not starting**: Ensure Docker is running and you have sufficient resources
1. **Port conflicts**: Check if ports 3000, 8000, or 6379 are already in use
1. **Permission errors**: Ensure proper file permissions for scripts and volumes
1. **Build failures**: Clear Docker cache with `docker system prune -f`

### Getting Help

- **Check the logs**: `pnpm logs` or `./scripts/dev.sh logs`
- **Verify health**: `pnpm health`
- **Clean restart**: `pnpm clean && pnpm dev`

## 📖 Documentation

- **[Contributing Guide](CONTRIBUTING.md)** - How to contribute to the project
- **[Deployment Guide](DEPLOYMENT.md)** - Production deployment instructions
- **[Monorepo Setup](MONOREPO_SETUP.md)** - Technical details of the monorepo structure
- **[Frontend README](frontend/README.md)** - SvelteKit frontend documentation
- **[Backend README](backend/README.md)** - FastAPI backend documentation
- **[Demo Processing README](backend/demo_processing/README.md)** - Microservice documentation

## 🔗 Quick Links

- **API Documentation**: <http://localhost:8000/docs> (when running)
- **Frontend**: <http://localhost:3000> (when running)
- **GitHub Repository**: [Your Repository URL]
- **Issues**: [Your Issues URL]
- **Discussions**: [Your Discussions URL]

## 📊 Project Status

- ✅ **Monorepo Setup**: Complete with pnpm workspaces and Python CLI
- ✅ **Development Environment**: Docker Compose with hot reloading
- ✅ **Production Environment**: Optimized Docker builds with Nginx
- ✅ **CI/CD Pipeline**: GitHub Actions for testing and deployment
- ✅ **Code Quality**: Linting, formatting, and type checking
- ✅ **Testing**: Unit, integration, and E2E tests
- 🚧 **Demo Upload UI**: In progress
- 🚧 **Advanced Analytics**: Planned
- 🚧 **User Authentication**: Planned

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details on:

- Setting up the development environment
- Code style and conventions
- Testing requirements
- Pull request process

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **SvelteKit** - Modern full-stack framework
- **FastAPI** - High-performance Python web framework
- **FastStream** - Async microservice framework
- **Shadcn/ui** - Beautiful component library
- **DemoParser2** - CS2 demo parsing library
- **AWPY** - CS2 analytics library

## 📞 Support

- **Documentation**: Check the README files in each service directory
- **Issues**: Report bugs and request features via GitHub Issues
- **Discussions**: Ask questions in GitHub Discussions
- **Community**: Join our community for help and collaboration

______________________________________________________________________

**Built with ❤️ for the CS2 community**
