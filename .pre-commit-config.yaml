# Pre-commit configuration for brainless-stats
# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

repos:
  # Markdown linting and fixing
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.45.0
    hooks:
      - id: markdownlint-fix

  # Basic pre-commit hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-merge-conflict

  # Python project validation
  - repo: https://github.com/abravalheri/validate-pyproject
    rev: v0.24.1
    hooks:
      - id: validate-pyproject

  # Markdown formatting
  - repo: https://github.com/executablebooks/mdformat
    rev: 0.7.22
    hooks:
      - id: mdformat

  # YAML formatting with Prettier
  - repo: https://github.com/rbubley/mirrors-prettier
    rev: v3.5.3
    hooks:
      - id: prettier
        types: [yaml]

  # Python linting and formatting with Ruff
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.9.9
    hooks:
      # Run the linter
      - id: ruff
        args: [--fix]
      # Run the formatter
      - id: ruff-format

  # Python type checking with basedpyright
  - repo: local
    hooks:
      - id: basedpyright
        name: basedpyright
        entry: uv run basedpyright
        language: system
        types: [python]
        require_serial: true

  # UV dependency management
  - repo: https://github.com/astral-sh/uv-pre-commit
    rev: 0.5.14
    hooks:
      # Compile requirements
      - id: uv-lock
