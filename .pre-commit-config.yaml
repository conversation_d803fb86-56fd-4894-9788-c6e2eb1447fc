# Pre-commit configuration for brainless-stats
# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

repos:
  # Markdown linting and fixing
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.45.0
    hooks:
      - id: markdownlint-fix

  # Basic pre-commit hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-merge-conflict
      - id: check-json
      - id: check-yaml
        args: ["--unsafe"] # Allow custom YAML tags
      - id: check-added-large-files
        args: ["--maxkb=1000"] # Limit files to 1MB

  # Python project validation
  - repo: https://github.com/abravalheri/validate-pyproject
    rev: v0.24.1
    hooks:
      - id: validate-pyproject

  # Security scanning for secrets (simple keyword detection)
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: detect-private-key

  # Spell checking
  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.6
    hooks:
      - id: codespell
        args: ["--write-changes"]
        exclude: ^(.*\.lock|.*\.min\.js|.*\.min\.css|pnpm-lock\.yaml|frontend/playwright-report/.*|.*package-lock\.json)$

  # Markdown formatting
  - repo: https://github.com/executablebooks/mdformat
    rev: 0.7.22
    hooks:
      - id: mdformat

  # Markdown link checking (disabled for now due to path issues)
  # - repo: https://github.com/tcort/markdown-link-check
  #   rev: v3.11.2
  #   hooks:
  #     - id: markdown-link-check
  #       args: ["--config", ".markdown-link-check.json"]

  # YAML formatting with Prettier
  - repo: https://github.com/rbubley/mirrors-prettier
    rev: v3.5.3
    hooks:
      - id: prettier
        types: [yaml]

  # Python linting and formatting with Ruff
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.9.9
    hooks:
      # Run the linter (only on staged files)
      - id: ruff
        args: [--fix]
        exclude: ^(packages/backend/migrate_to_raw_sql\.py|tasks\.py)$
      # Run the formatter
      - id: ruff-format
        exclude: ^(packages/backend/migrate_to_raw_sql\.py|tasks\.py)$

  # Python type checking with basedpyright (only on staged files)
  - repo: local
    hooks:
      - id: basedpyright
        name: basedpyright
        entry: uv run basedpyright --level error
        language: system
        types: [python]
        require_serial: true
        exclude: ^(packages/backend/migrate_to_raw_sql\.py|tasks\.py)$

  # UV dependency management
  - repo: https://github.com/astral-sh/uv-pre-commit
    rev: 0.5.14
    hooks:
      # Compile requirements
      - id: uv-lock
