"""Client for communicating with the demo processing microservice."""

import asyncio
import uuid
from types import TracebackType
from typing import Any

from faststream.redis import RedisBroker
from pydantic import BaseModel, Field

from ..logging_config import get_logger

logger = get_logger(__name__)

# Type alias for demo data - kept flexible to avoid tight coupling with demo processing service
DemoDataDict = dict[str, Any]


class DemoProcessingRequest(BaseModel):
    """Request model for processing a demo file."""

    demo_file_path: str = Field(..., description="Path to the demo file to process")
    request_id: str = Field(
        ..., description="Unique identifier for this processing request"
    )
    user_id: str | None = Field(
        None, description="User ID who requested the processing"
    )


class DemoProcessingResult(BaseModel):
    """Result of demo processing."""

    request_id: str = Field(..., description="Request ID that was processed")
    success: bool = Field(..., description="Whether processing was successful")
    demo_data: DemoDataDict | None = Field(None, description="Extracted demo data")
    error_message: str | None = Field(
        None, description="Error message if processing failed"
    )
    processing_time_seconds: float = Field(
        ..., description="Time taken to process the demo"
    )


class DemoProcessingError(BaseModel):
    """Error response for failed demo processing."""

    request_id: str = Field(..., description="Request ID that failed")
    error_type: str = Field(..., description="Type of error")
    error_message: str = Field(..., description="Detailed error message")
    timestamp: str = Field(..., description="Timestamp of the error")


class DemoProcessingClient:
    """Client for sending demo processing requests to the microservice."""

    def __init__(self, broker_url: str = "redis://localhost:6379"):
        """Initialize the client.

        Args:
            broker_url: Redis connection URL
        """
        self.broker_url: str = broker_url
        self._results: dict[str, DemoProcessingResult] = {}
        self._errors: dict[str, DemoProcessingError] = {}
        self._broker: RedisBroker | None = None
        self._is_started: bool = False

    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_broker_started()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        """Async context manager exit."""
        await self.close()

    async def _ensure_broker_started(self):
        """Ensure the broker is started."""
        if self._broker is None or not self._is_started:
            if self._broker is None:
                self._broker = RedisBroker(self.broker_url)

                # Set up result handler
                @self._broker.subscriber("demo.processing.results")
                async def handle_result(result: DemoProcessingResult):
                    """Handle processing results."""
                    logger.info(
                        "Received processing result",
                        request_id=result.request_id,
                        success=result.success,
                    )
                    self._results[result.request_id] = result

                # Set up error handler
                @self._broker.subscriber("demo.processing.errors")
                async def handle_error(error: DemoProcessingError):
                    """Handle processing errors."""
                    logger.error(
                        "Received processing error",
                        request_id=error.request_id,
                        error_type=error.error_type,
                        error_message=error.error_message,
                    )
                    self._errors[error.request_id] = error

            if not self._is_started:
                await self._broker.start()
                self._is_started = True

    async def request_demo_processing(
        self,
        demo_file_path: str,
        user_id: str | None = None,
        timeout_seconds: int = 300,
    ) -> DemoProcessingResult:
        """Request demo processing and wait for result.

        Args:
            demo_file_path: Path to the demo file to process
            user_id: Optional user ID who requested the processing
            timeout_seconds: Maximum time to wait for processing

        Returns:
            DemoProcessingResult with the processing outcome

        Raises:
            TimeoutError: If processing takes longer than timeout_seconds
            ConnectionError: If unable to connect to the microservice
        """
        await self._ensure_broker_started()

        # Generate unique request ID
        request_id = str(uuid.uuid4())

        # Create request
        request = DemoProcessingRequest(
            demo_file_path=demo_file_path,
            request_id=request_id,
            user_id=user_id,
        )

        logger.info(
            "Sending demo processing request",
            request_id=request_id,
            file_path=demo_file_path,
        )

        # Send request
        if self._broker is None:
            error_msg = "Broker not initialized"
            raise ConnectionError(error_msg)

        _ = await self._broker.publish(
            request.model_dump(),
            channel="demo.processing.requests",
        )

        # Wait for result
        start_time = asyncio.get_event_loop().time()

        while True:
            # Check for successful result
            if request_id in self._results:
                result = self._results.pop(request_id)
                logger.info("Retrieved processing result", request_id=request_id)
                return result

            # Check for error result
            if request_id in self._errors:
                error = self._errors.pop(request_id)
                logger.error("Retrieved processing error", request_id=request_id)
                # Create a failed result from the error
                return DemoProcessingResult(
                    request_id=request_id,
                    success=False,
                    demo_data=None,
                    error_message=f"{error.error_type}: {error.error_message}",
                    processing_time_seconds=asyncio.get_event_loop().time()
                    - start_time,
                )

            # Check timeout
            elapsed = asyncio.get_event_loop().time() - start_time
            if elapsed > timeout_seconds:
                error_msg = f"Demo processing request {request_id} timed out after {timeout_seconds} seconds"
                raise TimeoutError(error_msg)

            # Wait a bit before checking again
            await asyncio.sleep(0.1)

    async def request_demo_processing_async(
        self, demo_file_path: str, user_id: str | None = None
    ) -> str:
        """Request demo processing without waiting for result.

        Args:
            demo_file_path: Path to the demo file to process
            user_id: Optional user ID who requested the processing

        Returns:
            Request ID for tracking the processing

        Raises:
            ConnectionError: If unable to connect to the microservice
        """
        await self._ensure_broker_started()

        # Generate unique request ID
        request_id = str(uuid.uuid4())

        # Create request
        request = DemoProcessingRequest(
            demo_file_path=demo_file_path,
            request_id=request_id,
            user_id=user_id,
        )

        logger.info(
            "Sending async demo processing request",
            request_id=request_id,
            file_path=demo_file_path,
        )

        # Send request
        if self._broker is None:
            error_msg = "Broker not initialized"
            raise ConnectionError(error_msg)

        _ = await self._broker.publish(
            request.model_dump(), channel="demo.processing.requests"
        )

        return request_id

    async def get_result(self, request_id: str) -> DemoProcessingResult | None:
        """Get result for a specific request ID.

        Args:
            request_id: The request ID to get result for

        Returns:
            DemoProcessingResult if available, None otherwise
        """
        return self._results.pop(request_id, None)

    async def get_error(self, request_id: str) -> DemoProcessingError | None:
        """Get error for a specific request ID.

        Args:
            request_id: The request ID to get error for

        Returns:
            DemoProcessingError if available, None otherwise
        """
        return self._errors.pop(request_id, None)

    async def health_check(self, timeout_seconds: int = 10) -> dict[str, str]:
        """Check health of the demo processing service.

        Args:
            timeout_seconds: Maximum time to wait for health response

        Returns:
            Health status information

        Raises:
            TimeoutError: If health check times out
            ConnectionError: If unable to connect to the microservice
        """
        await self._ensure_broker_started()

        if self._broker is None:
            error_msg = "Broker not initialized"
            raise ConnectionError(error_msg)

        # Set up one-time health response handler
        health_result: dict[str, str] = {}

        @self._broker.subscriber("demo.processing.health.response")
        async def handle_health_response(response: dict[str, str]):
            """Handle health check response."""
            nonlocal health_result
            health_result.update(response)

        # Send health check request
        _ = await self._broker.publish({}, channel="demo.processing.health.check")

        # Wait for response
        start_time = asyncio.get_event_loop().time()

        while not health_result:
            elapsed = asyncio.get_event_loop().time() - start_time
            if elapsed > timeout_seconds:
                error_msg = f"Health check timed out after {timeout_seconds} seconds"
                raise TimeoutError(error_msg)

            await asyncio.sleep(0.1)

        return health_result

    async def close(self):
        """Close the client and cleanup resources."""
        if self._broker is not None and self._is_started:
            try:
                await self._broker.close()
            except Exception as e:
                logger.warning("Error closing broker", error=str(e))
            finally:
                self._broker = None
                self._is_started = False
                # Clear any pending results/errors
                self._results.clear()
                self._errors.clear()
